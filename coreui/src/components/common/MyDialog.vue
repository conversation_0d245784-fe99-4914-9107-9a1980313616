
<template>
  <transition name="dialog" appear>
    <div
      v-if="dialog"
      class="dialog-overlay"
      :style="{ zIndex: options.zIndex }"
      @click="onOverlayClick"
      @keydown.esc="cancel"
      tabindex="-1"
      ref="overlay"
    >
      <div class="dialog-card" :style="{ maxWidth: options.width + 'px' }">
        <div class="dialog-header" :style="{ backgroundColor: options.color }">
          <h3 class="dialog-title">{{ title }}</h3>
        </div>
        <div v-if="message" class="dialog-content">
          {{ message }}
        </div>
        <div class="dialog-actions">
          <button
            class="dialog-btn btn-primary"
            @click="agree"
            ref="confirmBtn"
          >
            {{ options.confirmText }}
          </button>
          <button
            class="dialog-btn btn-secondary"
            @click="cancel"
          >
            {{ options.cancelText }}
          </button>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  name: 'GlobalDialog',
  data() {
    return {
      dialog: false,
      resolve: null,
      reject: null,
      message: null,
      title: null,
      options: {
        color: '#1976d2',
        width: 400,
        zIndex: 1000000000,
        confirmText: 'Yes',
        cancelText: 'Cancel'
      }
    }
  },
  mounted() {
    // Focus management when dialog opens
    this.$watch('dialog', (newVal) => {
      if (newVal) {
        this.$nextTick(() => {
          if (this.$refs.overlay) {
            this.$refs.overlay.focus();
          }
          if (this.$refs.confirmBtn) {
            this.$refs.confirmBtn.focus();
          }
        });
      }
    });
  },
  methods: {
    open(title, message, options = {}) {
      this.dialog = true;
      this.title = title;
      this.message = message;
      this.options = { ...this.options, ...options };
      
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    agree() {
      if (this.resolve) {
        this.resolve(true);
      }
      this.close();
    },
    cancel() {
      if (this.resolve) {
        this.resolve(false);
      }
      this.close();
    },
    close() {
      this.dialog = false;
      this.resolve = null;
      this.reject = null;
    },
    onOverlayClick(event) {
      if (event.target === event.currentTarget) {
        this.cancel();
      }
    }
  }
}
</script>

<style scoped>


.dialog-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
}

.dialog-header {
  background: #1976d2;
  color: white;
  padding: 16px 20px;
}

.dialog-title {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.dialog-content {
  padding: 20px;
  color: #333;
  line-height: 1.5;
}

.dialog-actions {
  padding: 0 20px 20px 20px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.dialog-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.dialog-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.btn-primary {
  background: #1976d2;
  color: white;
}

.btn-primary:hover {
  background: #1565c0;
}

.btn-secondary {
  background: #757575;
  color: white;
}

.btn-secondary:hover {
  background: #616161;
}

/* Transitions */
.dialog-enter-active,
.dialog-leave-active {
  transition: opacity 0.3s ease;
}

.dialog-enter-active .dialog-card,
.dialog-leave-active .dialog-card {
  transition: transform 0.3s ease;
}

.dialog-enter,
.dialog-leave-to {
  opacity: 0;
}

.dialog-enter .dialog-card,
.dialog-leave-to .dialog-card {
  transform: scale(0.9);
}

.dialog-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 999999999 !important;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  outline: none;
}

</style>