
<template>
  <transition name="dialog" appear>
    <div
      v-if="dialog"
      class="dialog-overlay"
      :class="{ 'dialog-overlay--dark': $store.state.darkMode }"
      :style="{ zIndex: options.zIndex }"
      @click="onOverlayClick"
      @keydown.esc="cancel"
      tabindex="-1"
      ref="overlay"
    >
      <div
        class="dialog-card"
        :class="{ 'dialog-card--dark': $store.state.darkMode }"
        :style="{ maxWidth: options.width + 'px' }"
      >
        <!-- Header with gradient background -->
        <div
          class="dialog-header"
          :class="[
            { 'dialog-header--dark': $store.state.darkMode },
            `dialog-header--${options.color}`
          ]"
        >
          <div class="dialog-icon" v-if="options.icon">
            <svg viewBox="0 0 24 24" width="24" height="24">
              <path :d="getIconPath(options.icon)" fill="currentColor"/>
            </svg>
          </div>
          <h3 class="dialog-title">{{ title }}</h3>
          <button
            v-if="options.closable"
            @click="cancel"
            class="dialog-close"
            :class="{ 'dialog-close--dark': $store.state.darkMode }"
          >
            <svg viewBox="0 0 24 24" width="20" height="20">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" fill="currentColor"/>
            </svg>
          </button>
        </div>

        <!-- Content Section -->
        <div
          v-if="message"
          class="dialog-content"
          :class="{ 'dialog-content--dark': $store.state.darkMode }"
        >
          {{ message }}
        </div>

        <!-- Actions Section -->
        <div
          class="dialog-actions"
          :class="{ 'dialog-actions--dark': $store.state.darkMode }"
        >
          <button
            class="dialog-btn dialog-btn--primary"
            :class="{ 'dialog-btn--dark': $store.state.darkMode }"
            @click="agree"
            ref="confirmBtn"
          >
            {{ options.confirmText }}
          </button>
          <button
            class="dialog-btn dialog-btn--secondary"
            :class="{ 'dialog-btn--dark': $store.state.darkMode }"
            @click="cancel"
          >
            {{ options.cancelText }}
          </button>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'GlobalDialog',
  data() {
    return {
      dialog: false,
      resolve: null,
      reject: null,
      message: null,
      title: null,
      options: {
        color: 'primary',
        width: 400,
        zIndex: 1000000000,
        confirmText: 'Yes',
        cancelText: 'Cancel',
        icon: null,
        closable: true
      }
    }
  },
  computed: {
    ...mapState(['darkMode'])
  },
  mounted() {
    // Focus management when dialog opens
    this.$watch('dialog', (newVal) => {
      if (newVal) {
        this.$nextTick(() => {
          if (this.$refs.overlay) {
            this.$refs.overlay.focus();
          }
          if (this.$refs.confirmBtn) {
            this.$refs.confirmBtn.focus();
          }
        });
      }
    });
  },
  methods: {
    open(title, message, options = {}) {
      this.dialog = true;
      this.title = title;
      this.message = message;
      this.options = { ...this.options, ...options };

      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    agree() {
      if (this.resolve) {
        this.resolve(true);
      }
      this.close();
    },
    cancel() {
      if (this.resolve) {
        this.resolve(false);
      }
      this.close();
    },
    close() {
      this.dialog = false;
      this.resolve = null;
      this.reject = null;
    },
    onOverlayClick(event) {
      if (event.target === event.currentTarget && this.options.closable) {
        this.cancel();
      }
    },
    getIconPath(iconType) {
      const icons = {
        warning: 'M12 2L1 21h22L12 2zm0 3.99L19.53 19H4.47L12 5.99zM11 16h2v2h-2v-2zm0-6h2v4h-2v-4z',
        error: 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z',
        success: 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z',
        info: 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z',
        question: 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z'
      };
      return icons[iconType] || icons.info;
    }
  }
}
</script>

<style scoped>
/* ==============================================
   LIGHT THEME (DEFAULT)
   ============================================== */

/* Modal Card */
.dialog-card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  min-width: 400px;
  max-width: 90vw;
  animation: slideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

/* Dark theme modal */
.dialog-card--dark {
  background: #2d3748;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.6);
  color: #e2e8f0;
}

@keyframes slideIn {
  from {
    transform: translateY(-50px) scale(0.9);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

/* Header */
.dialog-header {
  padding: 20px 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
  overflow: hidden;
  transition: background 0.3s ease;
}

/* Header color variants */
.dialog-header--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.dialog-header--danger {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.dialog-header--success {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.dialog-header--warning {
  background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.dialog-header--info {
  background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
}

/* Dark theme header variants */
.dialog-header--dark.dialog-header--primary {
  background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
}

.dialog-header--dark.dialog-header--danger {
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
}

.dialog-header--dark.dialog-header--success {
  background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
}

.dialog-header--dark.dialog-header--warning {
  background: linear-gradient(135deg, #d69e2e 0%, #b7791f 100%);
}

.dialog-header--dark.dialog-header--info {
  background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%);
}

.dialog-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.dialog-title {
  color: white;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  flex: 1;
}

.dialog-close {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.dialog-close:hover {
  background: rgba(255, 255, 255, 0.1);
}

.dialog-close--dark:hover {
  background: rgba(255, 255, 255, 0.05);
}

/* Content */
.dialog-content {
  padding: 32px 24px 24px;
  color: #333;
  line-height: 1.6;
  font-size: 16px;
  transition: background-color 0.3s ease;
}

/* Dark theme content */
.dialog-content--dark {
  background-color: #2d3748;
  color: #e2e8f0;
}

/* Actions */
.dialog-actions {
  padding: 16px 24px 24px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  border-top: 1px solid #e9ecef;
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

/* Dark theme actions */
.dialog-actions--dark {
  background-color: #2d3748;
  border-top-color: #4a5568;
}

/* Button Styles */
.dialog-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
  min-width: 100px;
}

.dialog-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.dialog-btn:active {
  transform: translateY(0);
}

.dialog-btn--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.dialog-btn--primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
}

.dialog-btn--secondary {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(108, 117, 125, 0.4);
}

.dialog-btn--secondary:hover {
  background: linear-gradient(135deg, #5a6268 0%, #3d4449 100%);
  box-shadow: 0 8px 25px rgba(108, 117, 125, 0.6);
}

/* Dark theme button adjustments */
.dialog-btn--dark.dialog-btn--primary {
  background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
  box-shadow: 0 4px 15px rgba(74, 85, 104, 0.4);
}

.dialog-btn--dark.dialog-btn--primary:hover {
  background: linear-gradient(135deg, #3a4553 0%, #252d3a 100%);
  box-shadow: 0 8px 25px rgba(74, 85, 104, 0.6);
}

.dialog-btn--dark.dialog-btn--secondary {
  background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
  box-shadow: 0 4px 15px rgba(74, 85, 104, 0.4);
}

.dialog-btn--dark.dialog-btn--secondary:hover {
  background: linear-gradient(135deg, #3a4553 0%, #252d3a 100%);
  box-shadow: 0 8px 25px rgba(74, 85, 104, 0.6);
}

/* Overlay */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
  transition: background-color 0.3s ease;
  outline: none;
}

/* Dark theme overlay */
.dialog-overlay--dark {
  background: rgba(0, 0, 0, 0.8);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Transitions */
.dialog-enter-active,
.dialog-leave-active {
  transition: opacity 0.3s ease;
}

.dialog-enter-active .dialog-card,
.dialog-leave-active .dialog-card {
  transition: transform 0.3s ease;
}

.dialog-enter,
.dialog-leave-to {
  opacity: 0;
}

.dialog-enter .dialog-card,
.dialog-leave-to .dialog-card {
  transform: scale(0.9);
}

/* ==============================================
   RESPONSIVE DESIGN
   ============================================== */

@media (max-width: 480px) {
  .dialog-card {
    min-width: 320px;
    margin: 16px;
  }

  .dialog-header {
    padding: 16px 20px;
  }

  .dialog-title {
    font-size: 16px;
  }

  .dialog-content {
    padding: 24px 20px 20px;
  }

  .dialog-actions {
    padding: 12px 20px 20px;
    flex-direction: column;
  }

  .dialog-btn {
    width: 100%;
    margin-bottom: 8px;
  }
}

/* ==============================================
   ACCESSIBILITY & PREFERENCES
   ============================================== */

/* High contrast mode */
@media (prefers-contrast: high) {
  .dialog-header--primary {
    background: #000 !important;
  }

  .dialog-header--danger {
    background: #8b0000 !important;
  }

  .dialog-header--success {
    background: #006400 !important;
  }

  .dialog-header--warning {
    background: #ff8c00 !important;
  }

  .dialog-header--info {
    background: #000080 !important;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .dialog-overlay,
  .dialog-card,
  .dialog-btn {
    animation: none !important;
  }

  * {
    transition-duration: 0.1s !important;
  }
}

/* Focus states for accessibility */
.dialog-card:focus-within {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

.dialog-card--dark:focus-within {
  outline-color: #63b3ed;
}

.dialog-btn:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

.dialog-close:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .dialog-overlay {
    display: none !important;
  }
}
</style>